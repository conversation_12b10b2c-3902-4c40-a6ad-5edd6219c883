services:
  api-gateway:
    image: capcapcap/api-gateway:0.1.3-p
    container_name: api-gateway
    restart: always
    environment:
      VIRTUAL_HOST: route-transport.duckdns.org
    ports:
      - "8080:8080"
    networks:
      - capnet

  core-backend:
    image: capcapcap/core-backend:0.1.3-p
    container_name: core-backend
    restart: always
    env_file:
      - ./.env
    ports:
      - "8083:8083"
    networks:
      - capnet

  auth-server:
    image: capcapcap/auth-server:0.1.3-p
    container_name: auth-server
    restart: always
    env_file:
      - ./.env
    ports:
      - "8082:8082"
    networks:
      - capnet

  cap-ui:
    image: capcapcap/cap-ui:*******-p
    container_name: cap-ui
    restart: always
    environment:
      VIRTUAL_HOST: route-transport.duckdns.org
    ports:
      - "80:80"
    networks:
      - capnet

  redis:
    image: redis:7
    container_name: redis
    restart: always
    command: ["redis-server", "--appendonly", "yes"]
    volumes:
      - redis-data:/data
    networks:
      - capnet

networks:
  capnet:
    name: capnet
    driver: bridge
    external: true

volumes:
  redis-data:
